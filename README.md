# AI Clipper

An AI-powered video clipping application built with Flutter. This app automatically analyzes video content using speech transcription and AI to suggest the most interesting segments for creating short clips.

## Features

- **Automatic Video Analysis**: Uses FFmpeg to extract audio and Whisper AI for transcription
- **AI-Powered Clip Suggestions**: Analyzes transcripts to identify interesting moments
- **Smart Clipping**: Creates video clips based on AI suggestions
- **Cross-Platform**: Built with Flutter for iOS, Android, and desktop

## Architecture

### Services

- **FFmpegService**: Handles video processing, audio extraction, and clip creation
- **WhisperService**: Manages audio transcription using OpenAI's Whisper
- **AIAnalysisService**: Analyzes transcripts to identify interesting segments

### Controllers

- **VideoClipperController**: Orchestrates the complete AI clipping workflow

## Prerequisites

Before running the application, ensure you have the following installed:

1. **Flutter SDK** (3.9.0 or later)
2. **FFmpeg** - For video processing
3. **Whisper** - For audio transcription
   ```bash
   pip install openai-whisper
   ```

## Installation

1. Clone the repository:

   ```bash
   git clone <repository-url>
   cd aiclipper
   ```

2. Install dependencies:

   ```bash
   flutter pub get
   ```

3. Verify dependencies are available:
   ```bash
   flutter test test/controllers/video_clipper_controller_test.dart -t "validateDependencies"
   ```

## Testing

The project includes comprehensive tests for all components:

### Run All Tests

```bash
flutter test
```

### Run Specific Test Suites

```bash
# Service tests
flutter test test/services/

# Controller tests
flutter test test/controllers/

# Integration tests
flutter test test/integration/

# Run with coverage
flutter test --coverage
```

### Test Structure

- **Unit Tests**: Test individual services and components
- **Integration Tests**: Test complete workflows
- **Widget Tests**: Test UI components (basic Flutter app tests)

## Usage

### Basic Workflow

1. **Process a Video**:

   ```dart
   final controller = VideoClipperController();
   final suggestions = await controller.processVideo('/path/to/video.mp4');
   ```

2. **Create Clips**:
   ```dart
   for (final suggestion in suggestions) {
     final clipPath = await controller.createClip(
       '/path/to/video.mp4',
       suggestion['startTime'],
       suggestion['endTime'],
     );
     print('Created clip: $clipPath');
   }
   ```

### API Reference

#### VideoClipperController

- `processVideo(String videoPath)`: Analyzes video and returns clip suggestions
- `createClip(String videoPath, double start, double end)`: Creates a video clip
- `getVideoInfo(String videoPath)`: Gets video metadata
- `validateDependencies()`: Checks if required tools are available

#### ClipSuggestion

- `startTime`: Start time in seconds
- `endTime`: End time in seconds
- `confidence`: AI confidence score (0.0-1.0)
- `reason`: Why this segment was selected
- `text`: Transcript text for the segment

## Development

### Project Structure

```
lib/
├── controllers/          # Business logic controllers
├── services/            # Core services (FFmpeg, Whisper, AI)
└── main.dart           # Application entry point

test/
├── controllers/         # Controller tests
├── services/           # Service tests
├── integration/        # End-to-end tests
└── test_runner.dart   # Test suite runner
```

### Adding New Features

1. Create service classes in `lib/services/`
2. Add corresponding tests in `test/services/`
3. Update controllers to use new services
4. Add integration tests for complete workflows

## Troubleshooting

### Common Issues

1. **Whisper not found**: Ensure Whisper is installed and in PATH

   ```bash
   pip install openai-whisper
   whisper --help
   ```

2. **FFmpeg errors**: Verify FFmpeg installation

   ```bash
   ffmpeg -version
   ```

3. **Test failures**: Run dependency validation
   ```bash
   flutter test test/controllers/video_clipper_controller_test.dart -t "validateDependencies"
   ```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
