import 'package:aiclipper/controllers/video_clipper_controller.dart';
import 'package:aiclipper/services/ai_analysis_service.dart';

/// Demo script showing how to use the AI Clipper functionality
/// 
/// This script demonstrates:
/// 1. Processing a video to get AI-generated clip suggestions
/// 2. Creating clips from the suggestions
/// 3. Analyzing transcripts directly
void main() async {
  print('🎬 AI Clipper Demo');
  print('==================');
  
  // Initialize the controller
  final controller = VideoClipperController();
  
  // Check if dependencies are available
  print('\n📋 Checking dependencies...');
  final depsValid = await controller.validateDependencies();
  print('Dependencies available: ${depsValid ? "✅" : "❌"}');
  
  if (!depsValid) {
    print('\n⚠️  Required dependencies not found:');
    print('   - FFmpeg: Required for video processing');
    print('   - Whisper: Required for audio transcription');
    print('   Install with: pip install openai-whisper');
    return;
  }
  
  // Demo 1: Analyze a transcript directly
  print('\n🧠 Demo 1: AI Transcript Analysis');
  print('----------------------------------');
  
  await demoTranscriptAnalysis();
  
  // Demo 2: Complete video processing workflow (would require real video)
  print('\n🎥 Demo 2: Video Processing Workflow');
  print('------------------------------------');
  
  await demoVideoProcessing(controller);
  
  print('\n✨ Demo completed!');
}

/// Demonstrates AI transcript analysis with sample data
Future<void> demoTranscriptAnalysis() async {
  final aiService = AIAnalysisService();
  
  // Sample transcript data (simulating Whisper output)
  final sampleTranscript = {
    'segments': [
      {
        'text': 'Welcome to today\'s tutorial on Flutter development.',
        'start': 0.0,
        'end': 3.5,
      },
      {
        'text': 'This is going to be absolutely amazing!',
        'start': 5.0,
        'end': 8.0,
      },
      {
        'text': 'Let me show you this incredible new feature that will blow your mind.',
        'start': 10.0,
        'end': 14.0,
      },
      {
        'text': 'Here\'s an important tip that will save you hours of debugging.',
        'start': 16.0,
        'end': 20.0,
      },
      {
        'text': 'That\'s hilarious! I can\'t believe that just happened.',
        'start': 22.0,
        'end': 25.0,
      },
      {
        'text': 'What do you think about this approach? Let me know in the comments.',
        'start': 27.0,
        'end': 31.0,
      },
      {
        'text': 'This is just some regular content without special keywords.',
        'start': 33.0,
        'end': 37.0,
      },
      {
        'text': 'How to solve this problem step by step - it\'s easier than you think!',
        'start': 39.0,
        'end': 43.0,
      },
    ]
  };
  
  print('Analyzing transcript with ${sampleTranscript['segments']!.length} segments...');
  
  final suggestions = await aiService.analyzeTranscript(sampleTranscript, 60.0);
  
  print('\n🎯 Found ${suggestions.length} interesting clip suggestions:');
  print('');
  
  for (int i = 0; i < suggestions.length; i++) {
    final suggestion = suggestions[i];
    print('${i + 1}. ${suggestion.reason}');
    print('   ⏱️  ${suggestion.startTime.toStringAsFixed(1)}s - ${suggestion.endTime.toStringAsFixed(1)}s (${suggestion.duration.toStringAsFixed(1)}s)');
    print('   🎯 Confidence: ${(suggestion.confidence * 100).toStringAsFixed(0)}%');
    print('   💬 "${suggestion.text}"');
    print('');
  }
  
  // Show JSON output
  print('📄 JSON Output:');
  final jsonOutput = suggestions.map((s) => s.toJson()).toList();
  print(jsonOutput.toString());
}

/// Demonstrates the complete video processing workflow
Future<void> demoVideoProcessing(VideoClipperController controller) async {
  // This would work with a real video file
  const sampleVideoPath = '/path/to/your/video.mp4';
  
  print('This demo shows how to process a real video file:');
  print('');
  print('1. 📹 Load video: $sampleVideoPath');
  print('2. 🎵 Extract audio using FFmpeg');
  print('3. 🗣️  Transcribe audio using Whisper AI');
  print('4. 🧠 Analyze transcript for interesting moments');
  print('5. ✂️  Create clips from suggestions');
  print('');
  
  print('Example code:');
  print('```dart');
  print('// Process video and get AI suggestions');
  print('final suggestions = await controller.processVideo(videoPath);');
  print('');
  print('// Create clips from suggestions');
  print('for (final suggestion in suggestions) {');
  print('  final clipPath = await controller.createClip(');
  print('    videoPath,');
  print('    suggestion["startTime"],');
  print('    suggestion["endTime"],');
  print('  );');
  print('  print("Created clip: \$clipPath");');
  print('}');
  print('```');
  print('');
  
  // Try to get video info (will fail for non-existent file)
  try {
    final videoInfo = await controller.getVideoInfo(sampleVideoPath);
    if (videoInfo != null) {
      print('✅ Video info: $videoInfo');
    } else {
      print('❌ Could not get video info (file not found)');
    }
  } catch (e) {
    print('❌ Video processing would fail: $e');
  }
  
  print('');
  print('💡 To test with a real video:');
  print('   1. Place a video file in your project');
  print('   2. Update the path in this demo');
  print('   3. Ensure FFmpeg and Whisper are installed');
  print('   4. Run: dart run example/ai_clipper_demo.dart');
}
