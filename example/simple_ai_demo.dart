import 'dart:math';

/// Simple AI Analysis Demo (without Flutter dependencies)
/// 
/// This demonstrates the core AI transcript analysis logic
/// that powers the AI Clipper functionality.

void main() async {
  print('🧠 AI Clipper - Transcript Analysis Demo');
  print('==========================================');
  
  final analyzer = SimpleAIAnalyzer();
  
  // Sample transcript data (simulating Whisper output)
  final sampleTranscript = {
    'segments': [
      {
        'text': 'Welcome to today\'s tutorial on Flutter development.',
        'start': 0.0,
        'end': 3.5,
      },
      {
        'text': 'This is going to be absolutely amazing!',
        'start': 5.0,
        'end': 8.0,
      },
      {
        'text': 'Let me show you this incredible new feature that will blow your mind.',
        'start': 10.0,
        'end': 14.0,
      },
      {
        'text': 'Here\'s an important tip that will save you hours of debugging.',
        'start': 16.0,
        'end': 20.0,
      },
      {
        'text': 'That\'s hilarious! I can\'t believe that just happened.',
        'start': 22.0,
        'end': 25.0,
      },
      {
        'text': 'What do you think about this approach? Let me know in the comments.',
        'start': 27.0,
        'end': 31.0,
      },
      {
        'text': 'This is just some regular content without special keywords.',
        'start': 33.0,
        'end': 37.0,
      },
      {
        'text': 'How to solve this problem step by step - it\'s easier than you think!',
        'start': 39.0,
        'end': 43.0,
      },
    ]
  };
  
  print('\n📝 Analyzing transcript with ${sampleTranscript['segments']!.length} segments...');
  
  final suggestions = analyzer.analyzeTranscript(sampleTranscript, 60.0);
  
  print('\n🎯 Found ${suggestions.length} interesting clip suggestions:');
  print('');
  
  for (int i = 0; i < suggestions.length; i++) {
    final suggestion = suggestions[i];
    print('${i + 1}. ${suggestion.reason}');
    print('   ⏱️  ${suggestion.startTime.toStringAsFixed(1)}s - ${suggestion.endTime.toStringAsFixed(1)}s (${suggestion.duration.toStringAsFixed(1)}s)');
    print('   🎯 Confidence: ${(suggestion.confidence * 100).toStringAsFixed(0)}%');
    print('   💬 "${suggestion.text}"');
    print('');
  }
  
  print('✨ Demo completed!');
  print('');
  print('💡 This shows how the AI analyzes video transcripts to identify');
  print('   the most interesting moments for creating short clips.');
}

/// Simplified version of AIAnalysisService for demo purposes
class SimpleAIAnalyzer {
  List<ClipSuggestion> analyzeTranscript(Map<String, dynamic> transcript, double videoDuration) {
    final suggestions = <ClipSuggestion>[];
    
    // Extract segments from transcript
    final segments = transcript['segments'] as List<dynamic>? ?? [];
    
    if (segments.isEmpty) {
      return suggestions;
    }
    
    // Analyze each segment for interesting content
    for (int i = 0; i < segments.length; i++) {
      final segment = segments[i] as Map<String, dynamic>;
      final text = segment['text'] as String? ?? '';
      final start = (segment['start'] as num?)?.toDouble() ?? 0.0;
      final end = (segment['end'] as num?)?.toDouble() ?? 0.0;
      
      // Score this segment based on various criteria
      final score = _scoreSegment(text);
      
      if (score > 0.6) { // Threshold for interesting content
        suggestions.add(ClipSuggestion(
          startTime: max(0.0, start - 2.0), // Add 2s context before
          endTime: min(videoDuration, end + 3.0), // Add 3s context after
          confidence: score,
          reason: _getReasonForClip(text),
          text: text,
        ));
      }
    }
    
    // Sort by confidence and return top 5
    suggestions.sort((a, b) => b.confidence.compareTo(a.confidence));
    return suggestions.take(5).toList();
  }
  
  double _scoreSegment(String text) {
    double score = 0.0;
    final lowerText = text.toLowerCase();
    
    // Keywords that indicate interesting content
    final interestingKeywords = [
      'amazing', 'incredible', 'wow', 'unbelievable', 'fantastic',
      'hilarious', 'funny', 'laugh', 'joke', 'comedy',
      'important', 'key', 'crucial', 'essential', 'critical',
      'breakthrough', 'discovery', 'reveal', 'secret', 'surprise',
      'question', 'answer', 'explain', 'how to', 'tutorial',
      'tip', 'trick', 'hack', 'advice', 'recommendation'
    ];
    
    // Score based on keywords
    for (final keyword in interestingKeywords) {
      if (lowerText.contains(keyword)) {
        score += 0.25;
      }
    }
    
    // Score based on punctuation (excitement, questions)
    if (text.contains('!')) score += 0.15;
    if (text.contains('?')) score += 0.2;
    if (text.contains('...')) score += 0.1;
    
    // Score based on length (not too short, not too long)
    final wordCount = text.split(' ').length;
    if (wordCount >= 3 && wordCount <= 50) {
      score += 0.15;
    }
    
    // Boost score for very interesting combinations
    if (lowerText.contains('incredible') && text.contains('!')) {
      score += 0.2;
    }
    
    return min(score, 1.0); // Cap at 1.0
  }
  
  String _getReasonForClip(String text) {
    final lowerText = text.toLowerCase();
    
    // Check for educational content first (more specific)
    if (lowerText.contains('how to') || lowerText.contains('tutorial') || 
        lowerText.contains('tip') || lowerText.contains('step by step')) {
      return 'Educational content';
    }
    if (lowerText.contains('funny') || lowerText.contains('hilarious') || 
        lowerText.contains('joke') || lowerText.contains('laugh')) {
      return 'Funny moment';
    }
    if (lowerText.contains('important') || lowerText.contains('key') || 
        lowerText.contains('crucial')) {
      return 'Key information';
    }
    if (lowerText.contains('amazing') || lowerText.contains('incredible') || 
        lowerText.contains('wow')) {
      return 'Exciting moment';
    }
    if (text.contains('?')) {
      return 'Important question';
    }
    
    return 'Interesting content';
  }
}

/// Simple clip suggestion class for demo
class ClipSuggestion {
  final double startTime;
  final double endTime;
  final double confidence;
  final String reason;
  final String text;
  
  ClipSuggestion({
    required this.startTime,
    required this.endTime,
    required this.confidence,
    required this.reason,
    required this.text,
  });
  
  double get duration => endTime - startTime;
}
