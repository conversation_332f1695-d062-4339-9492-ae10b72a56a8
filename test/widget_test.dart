// AI Clipper Widget Tests
//
// This file contains widget tests for the AI Clipper application.
// For unit tests of the AI clipping functionality, see the services/ and controllers/ test directories.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:aiclipper/main.dart';

void main() {
  testWidgets('Counter increments smoke test', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const MyApp());

    // Verify that our counter starts at 0.
    expect(find.text('0'), findsOneWidget);
    expect(find.text('1'), findsNothing);

    // Tap the '+' icon and trigger a frame.
    await tester.tap(find.byIcon(Icons.add));
    await tester.pump();

    // Verify that our counter has incremented.
    expect(find.text('0'), findsNothing);
    expect(find.text('1'), findsOneWidget);
  });
}
