import 'package:flutter_test/flutter_test.dart';
import 'package:aiclipper/controllers/video_clipper_controller.dart';
import 'package:aiclipper/services/ai_analysis_service.dart';
import 'dart:io';

void main() {
  group('AI Clipper Integration Tests', () {
    late VideoClipperController controller;

    setUp(() {
      controller = VideoClipperController();
    });

    group('End-to-End Workflow', () {
      test('should handle complete AI clipping workflow with mock data', () async {
        // Test the complete workflow with error handling
        const videoPath = '/tmp/test_video.mp4';

        // Step 1: Validate dependencies
        final depsValid = await controller.validateDependencies();
        expect(depsValid, isA<bool>());

        // Step 2: Get video info (will fail for non-existent file)
        final videoInfo = await controller.getVideoInfo(videoPath);
        expect(videoInfo, isNull);

        // Step 3: Process video (will fail for non-existent file)
        try {
          final suggestions = await controller.processVideo(videoPath);
          // If we get here, the video exists and processing worked
          expect(suggestions, isA<List<Map<String, dynamic>>>());
          
          // Validate suggestion structure
          for (final suggestion in suggestions) {
            expect(suggestion, containsPair('startTime', isA<double>()));
            expect(suggestion, containsPair('endTime', isA<double>()));
            expect(suggestion, containsPair('confidence', isA<double>()));
            expect(suggestion, containsPair('reason', isA<String>()));
            expect(suggestion, containsPair('text', isA<String>()));
            expect(suggestion, containsPair('duration', isA<double>()));
          }
          
          // Step 4: Create clips from suggestions
          for (final suggestion in suggestions.take(2)) { // Test first 2 suggestions
            final startTime = suggestion['startTime'] as double;
            final endTime = suggestion['endTime'] as double;
            
            try {
              final clipPath = await controller.createClip(videoPath, startTime, endTime);
              expect(clipPath, isA<String>());
              expect(clipPath, isNotEmpty);
              
              // Verify clip file would be created (can't actually verify without real video)
              expect(clipPath, contains('_clip_'));
            } catch (e) {
              // Expected to fail without real video file
              expect(e, isA<Exception>());
            }
          }
          
        } catch (e) {
          // Expected when video file doesn't exist
          expect(e, isA<Exception>());
          expect(e.toString(), contains('Video file not found'));
        }
      });

      test('should handle AI analysis with realistic transcript data', () async {
        final aiService = AIAnalysisService();
        
        // Create a realistic transcript with various types of content
        final transcript = {
          'segments': [
            {
              'text': 'Welcome to today\'s tutorial on Flutter development.',
              'start': 0.0,
              'end': 3.0,
            },
            {
              'text': 'This is going to be absolutely amazing!',
              'start': 5.0,
              'end': 8.0,
            },
            {
              'text': 'Let me show you this incredible new feature.',
              'start': 10.0,
              'end': 13.0,
            },
            {
              'text': 'Here\'s an important tip that will save you hours.',
              'start': 15.0,
              'end': 18.0,
            },
            {
              'text': 'That\'s hilarious! I can\'t believe that happened.',
              'start': 20.0,
              'end': 23.0,
            },
            {
              'text': 'What do you think about this approach?',
              'start': 25.0,
              'end': 27.0,
            },
            {
              'text': 'This is just regular content without keywords.',
              'start': 30.0,
              'end': 33.0,
            },
            {
              'text': 'How to solve this problem step by step.',
              'start': 35.0,
              'end': 38.0,
            },
          ]
        };

        final suggestions = await aiService.analyzeTranscript(transcript, 60.0);

        // Verify we get meaningful suggestions
        expect(suggestions, isNotEmpty);
        expect(suggestions.length, lessThanOrEqualTo(5));

        // Verify suggestions are sorted by confidence
        for (int i = 0; i < suggestions.length - 1; i++) {
          expect(suggestions[i].confidence, greaterThanOrEqualTo(suggestions[i + 1].confidence));
        }

        // Verify high-confidence suggestions are included
        final highConfidenceSuggestions = suggestions.where((s) => s.confidence > 0.6).toList();
        expect(highConfidenceSuggestions, isNotEmpty);

        // Verify different types of interesting content are detected
        final reasons = suggestions.map((s) => s.reason).toSet();
        expect(reasons.length, greaterThan(1)); // Should have multiple types

        // Verify time ranges are reasonable
        for (final suggestion in suggestions) {
          expect(suggestion.startTime, greaterThanOrEqualTo(0.0));
          expect(suggestion.endTime, lessThanOrEqualTo(60.0));
          expect(suggestion.endTime, greaterThan(suggestion.startTime));
          expect(suggestion.duration, greaterThan(0.0));
        }
      });

      test('should handle error scenarios gracefully', () async {
        // Test various error scenarios that might occur in production

        // 1. Missing video file
        expect(
          () => controller.processVideo('/non/existent/video.mp4'),
          throwsA(isA<Exception>()),
        );

        // 2. Invalid time ranges for clipping
        expect(
          () => controller.createClip('/tmp/video.mp4', -1.0, 10.0),
          throwsA(isA<Exception>()),
        );

        expect(
          () => controller.createClip('/tmp/video.mp4', 10.0, 5.0),
          throwsA(isA<Exception>()),
        );

        // 3. Empty transcript
        final aiService = AIAnalysisService();
        final emptyTranscript = {'segments': <dynamic>[]};
        final suggestions = await aiService.analyzeTranscript(emptyTranscript, 100.0);
        expect(suggestions, isEmpty);

        // 4. Malformed transcript
        final malformedTranscript = {'segments': null};
        final malformedSuggestions = await aiService.analyzeTranscript(malformedTranscript, 100.0);
        expect(malformedSuggestions, isEmpty);
      });

      test('should handle concurrent processing requests', () async {
        // Test multiple concurrent video processing requests
        final futures = <Future>[];
        
        for (int i = 0; i < 3; i++) {
          futures.add(
            controller.processVideo('/tmp/test_video_$i.mp4').catchError((e) => <Map<String, dynamic>>[]),
          );
        }

        final results = await Future.wait(futures);
        expect(results, hasLength(3));
        
        // All should return empty lists due to missing files
        for (final result in results) {
          expect(result, isA<List<Map<String, dynamic>>>());
        }
      });

      test('should validate clip suggestion quality', () async {
        final aiService = AIAnalysisService();
        
        // Test with high-quality content
        final highQualityTranscript = {
          'segments': [
            {
              'text': 'This is absolutely incredible and amazing!',
              'start': 0.0,
              'end': 3.0,
            },
            {
              'text': 'Here\'s the most important tip you\'ll ever learn!',
              'start': 5.0,
              'end': 8.0,
            },
          ]
        };

        final highQualitySuggestions = await aiService.analyzeTranscript(highQualityTranscript, 20.0);
        expect(highQualitySuggestions, isNotEmpty);
        expect(highQualitySuggestions.every((s) => s.confidence > 0.6), isTrue);

        // Test with low-quality content
        final lowQualityTranscript = {
          'segments': [
            {
              'text': 'This is just regular content.',
              'start': 0.0,
              'end': 3.0,
            },
            {
              'text': 'Nothing special here.',
              'start': 5.0,
              'end': 8.0,
            },
          ]
        };

        final lowQualitySuggestions = await aiService.analyzeTranscript(lowQualityTranscript, 20.0);
        // Should have fewer or no suggestions for low-quality content
        expect(lowQualitySuggestions.length, lessThan(highQualitySuggestions.length));
      });
    });

    group('Performance Tests', () {
      test('should handle large transcripts efficiently', () async {
        final aiService = AIAnalysisService();
        
        // Create a large transcript with 100 segments
        final segments = <Map<String, dynamic>>[];
        for (int i = 0; i < 100; i++) {
          segments.add({
            'text': i % 10 == 0 
              ? 'This is amazing content number $i!' 
              : 'Regular content segment $i.',
            'start': i * 2.0,
            'end': i * 2.0 + 1.5,
          });
        }

        final largeTranscript = {'segments': segments};
        
        final stopwatch = Stopwatch()..start();
        final suggestions = await aiService.analyzeTranscript(largeTranscript, 200.0);
        stopwatch.stop();

        // Should complete in reasonable time (less than 5 seconds)
        expect(stopwatch.elapsedMilliseconds, lessThan(5000));
        
        // Should still return reasonable number of suggestions
        expect(suggestions.length, lessThanOrEqualTo(5));
        expect(suggestions, isNotEmpty);
      });

      test('should handle memory efficiently with large data', () async {
        // Test memory usage with large text content
        final aiService = AIAnalysisService();
        
        final largeTextSegment = 'This is amazing! ' * 1000; // Large text
        final transcript = {
          'segments': [
            {
              'text': largeTextSegment,
              'start': 0.0,
              'end': 10.0,
            },
          ]
        };

        // Should not crash or hang
        final suggestions = await aiService.analyzeTranscript(transcript, 20.0);
        expect(suggestions, isNotNull);
      });
    });
  });
}
