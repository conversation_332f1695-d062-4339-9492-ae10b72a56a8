import 'package:flutter_test/flutter_test.dart';
import 'package:aiclipper/services/ai_analysis_service.dart';

void main() {
  group('AIAnalysisService', () {
    late AIAnalysisService service;

    setUp(() {
      service = AIAnalysisService();
    });

    group('analyzeTranscript', () {
      test('should return empty list for empty transcript', () async {
        final transcript = {'segments': <dynamic>[]};
        final result = await service.analyzeTranscript(transcript, 100.0);

        expect(result, isEmpty);
      });

      test(
        'should identify interesting segments with high confidence keywords',
        () async {
          final transcript = {
            'segments': [
              {
                'text':
                    'This is amazing! I can\'t believe how incredible this is!',
                'start': 10.0,
                'end': 15.0,
              },
              {
                'text': 'Just some regular content here.',
                'start': 20.0,
                'end': 25.0,
              },
              {
                'text': 'Here\'s an important tip that will change everything!',
                'start': 30.0,
                'end': 35.0,
              },
            ],
          };

          final result = await service.analyzeTranscript(transcript, 100.0);

          expect(result, isNotEmpty);
          expect(result.length, greaterThanOrEqualTo(2));

          // Check that high-confidence segments are included
          final amazingClip = result.firstWhere(
            (clip) => clip.text.contains('amazing'),
            orElse: () => throw Exception('Amazing clip not found'),
          );
          expect(amazingClip.confidence, greaterThan(0.6));
          expect(amazingClip.reason, contains('Exciting'));

          final importantClip = result.firstWhere(
            (clip) => clip.text.contains('important'),
            orElse: () => throw Exception('Important clip not found'),
          );
          expect(importantClip.confidence, greaterThan(0.6));
          expect(importantClip.reason, contains('Educational'));
        },
      );

      test('should expand clip times with context', () async {
        final transcript = {
          'segments': [
            {
              'text': 'This is absolutely incredible!',
              'start': 10.0,
              'end': 12.0,
            },
          ],
        };

        final result = await service.analyzeTranscript(transcript, 100.0);

        expect(result, isNotEmpty);
        final clip = result.first;

        // Should expand start time (but not below 0)
        expect(clip.startTime, lessThanOrEqualTo(10.0));
        expect(clip.startTime, greaterThanOrEqualTo(0.0));

        // Should expand end time
        expect(clip.endTime, greaterThanOrEqualTo(12.0));
        expect(clip.endTime, lessThanOrEqualTo(100.0));
      });

      test('should merge overlapping suggestions', () async {
        final transcript = {
          'segments': [
            {'text': 'This is amazing!', 'start': 10.0, 'end': 12.0},
            {'text': 'Absolutely incredible!', 'start': 13.0, 'end': 15.0},
            {'text': 'Wow, just wow!', 'start': 16.0, 'end': 18.0},
          ],
        };

        final result = await service.analyzeTranscript(transcript, 100.0);

        // Should merge close segments into fewer clips
        expect(result.length, lessThan(3));

        if (result.isNotEmpty) {
          final mergedClip = result.first;
          expect(mergedClip.text, contains('amazing'));
          expect(mergedClip.text, contains('incredible'));
        }
      });

      test('should sort suggestions by confidence', () async {
        final transcript = {
          'segments': [
            {'text': 'This is okay content.', 'start': 10.0, 'end': 12.0},
            {
              'text': 'This is absolutely amazing and incredible!',
              'start': 20.0,
              'end': 22.0,
            },
            {'text': 'This is pretty good.', 'start': 30.0, 'end': 32.0},
          ],
        };

        final result = await service.analyzeTranscript(transcript, 100.0);

        if (result.length > 1) {
          // Should be sorted by confidence (highest first)
          for (int i = 0; i < result.length - 1; i++) {
            expect(
              result[i].confidence,
              greaterThanOrEqualTo(result[i + 1].confidence),
            );
          }
        }
      });

      test('should limit to top 5 suggestions', () async {
        final segments = <Map<String, dynamic>>[];

        // Create 10 interesting segments
        for (int i = 0; i < 10; i++) {
          segments.add({
            'text': 'This is amazing content number $i!',
            'start': i * 10.0,
            'end': i * 10.0 + 2.0,
          });
        }

        final transcript = {'segments': segments};
        final result = await service.analyzeTranscript(transcript, 200.0);

        expect(result.length, lessThanOrEqualTo(5));
      });

      test('should handle different types of interesting content', () async {
        final transcript = {
          'segments': [
            {
              'text': 'That\'s hilarious! I can\'t stop laughing!',
              'start': 10.0,
              'end': 12.0,
            },
            {
              'text': 'Here\'s how to solve this problem step by step.',
              'start': 20.0,
              'end': 22.0,
            },
            {
              'text': 'What do you think about this approach?',
              'start': 30.0,
              'end': 32.0,
            },
          ],
        };

        final result = await service.analyzeTranscript(transcript, 100.0);

        expect(result, isNotEmpty);

        final reasons = result.map((clip) => clip.reason).toSet();

        // Should detect at least the funny moment (highest confidence)
        expect(reasons, contains('Funny moment'));

        // Should have at least one type of interesting content
        expect(reasons.length, greaterThan(0));
      });
    });

    group('ClipSuggestion', () {
      test('should calculate duration correctly', () {
        final suggestion = ClipSuggestion(
          startTime: 10.0,
          endTime: 25.0,
          confidence: 0.8,
          reason: 'Test',
          text: 'Test text',
        );

        expect(suggestion.duration, equals(15.0));
      });

      test('should convert to JSON correctly', () {
        final suggestion = ClipSuggestion(
          startTime: 10.5,
          endTime: 25.3,
          confidence: 0.85,
          reason: 'Test reason',
          text: 'Test text content',
        );

        final json = suggestion.toJson();

        expect(json['startTime'], equals(10.5));
        expect(json['endTime'], equals(25.3));
        expect(json['duration'], equals(14.8));
        expect(json['confidence'], equals(0.85));
        expect(json['reason'], equals('Test reason'));
        expect(json['text'], equals('Test text content'));
      });

      test('should have meaningful toString', () {
        final suggestion = ClipSuggestion(
          startTime: 10.0,
          endTime: 25.0,
          confidence: 0.8,
          reason: 'Test',
          text: 'Test text',
        );

        final string = suggestion.toString();
        expect(string, contains('10.0s-25.0s'));
        expect(string, contains('0.80'));
        expect(string, contains('Test'));
      });
    });
  });
}
