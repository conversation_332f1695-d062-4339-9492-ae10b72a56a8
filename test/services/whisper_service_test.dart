import 'package:flutter_test/flutter_test.dart';
import 'package:aiclipper/services/whisper_service.dart';
import 'dart:io';

void main() {
  group('WhisperService', () {
    late WhisperService service;

    setUp(() {
      service = WhisperService();
    });

    group('transcribeAudio', () {
      test('should handle non-existent audio file', () async {
        const audioPath = '/non/existent/audio.wav';

        expect(
          () => service.transcribeAudio(audioPath),
          throwsA(isA<Exception>()),
        );
      });

      test('should handle empty file path', () async {
        expect(
          () => service.transcribeAudio(''),
          throwsA(isA<Exception>()),
        );
      });

      test('should handle invalid audio file format', () async {
        // Create a temporary text file (not an audio file)
        final tempFile = File('/tmp/fake_audio.wav');
        await tempFile.writeAsString('This is not an audio file');

        try {
          expect(
            () => service.transcribeAudio(tempFile.path),
            throwsA(isA<Exception>()),
          );
        } finally {
          // Clean up
          if (await tempFile.exists()) {
            await tempFile.delete();
          }
        }
      });

      test('should throw exception with meaningful message for missing Whisper', () async {
        // This test assumes Whisper is not installed or not in PATH
        const audioPath = '/tmp/test_audio.wav';

        try {
          await service.transcribeAudio(audioPath);
          // If we get here, Whisper is actually installed
          fail('Expected exception due to missing Whisper or invalid file');
        } catch (e) {
          expect(e, isA<Exception>());
          expect(e.toString(), contains('Failed to transcribe'));
        }
      });

      test('should handle special characters in file paths', () async {
        const specialPath = '/tmp/test audio with spaces & symbols!.wav';

        expect(
          () => service.transcribeAudio(specialPath),
          throwsA(isA<Exception>()),
        );
      });

      test('should validate that result is JSON when successful', () async {
        // This test would require a real audio file and Whisper installation
        // For now, we'll test the error handling path
        
        const audioPath = '/tmp/test_audio.wav';
        
        try {
          final result = await service.transcribeAudio(audioPath);
          // If we get here, the transcription was successful
          expect(result, isA<Map<String, dynamic>>());
          expect(result, isNotEmpty);
        } catch (e) {
          // Expected when Whisper is not available or file doesn't exist
          expect(e, isA<Exception>());
        }
      });
    });

    group('Error scenarios', () {
      test('should handle Whisper command not found', () async {
        // This simulates the case where Whisper is not installed
        const audioPath = '/tmp/test_audio.wav';

        expect(
          () => service.transcribeAudio(audioPath),
          throwsA(
            predicate((e) => 
              e is Exception && 
              e.toString().contains('Failed to transcribe')
            ),
          ),
        );
      });

      test('should handle Whisper returning non-zero exit code', () async {
        // Create a temporary file that exists but is not a valid audio file
        final tempFile = File('/tmp/invalid_audio.wav');
        await tempFile.writeAsString('Invalid audio content');

        try {
          expect(
            () => service.transcribeAudio(tempFile.path),
            throwsA(
              predicate((e) => 
                e is Exception && 
                (e.toString().contains('Whisper transcription failed') ||
                 e.toString().contains('Failed to transcribe'))
              ),
            ),
          );
        } finally {
          // Clean up
          if (await tempFile.exists()) {
            await tempFile.delete();
          }
        }
      });

      test('should handle invalid JSON response from Whisper', () async {
        // This would test the case where Whisper returns invalid JSON
        // Since we can't easily mock Process.run in this test setup,
        // we'll focus on testing the error handling structure
        
        const audioPath = '/tmp/test_audio.wav';
        
        try {
          await service.transcribeAudio(audioPath);
          fail('Expected exception for non-existent file');
        } catch (e) {
          expect(e, isA<Exception>());
          expect(e.toString(), contains('Failed to transcribe'));
        }
      });
    });

    group('Integration scenarios', () {
      test('should handle complete transcription workflow', () async {
        // Test the complete workflow with various file types
        final testFiles = [
          '/tmp/test1.wav',
          '/tmp/test2.mp3',
          '/tmp/test3.m4a',
        ];

        for (final filePath in testFiles) {
          expect(
            () => service.transcribeAudio(filePath),
            throwsA(isA<Exception>()),
          );
        }
      });

      test('should handle concurrent transcription requests', () async {
        // Test multiple simultaneous transcription requests
        final futures = <Future>[];
        
        for (int i = 0; i < 3; i++) {
          futures.add(
            service.transcribeAudio('/tmp/test_$i.wav').catchError((e) => null),
          );
        }

        // All should complete (with errors due to missing files)
        final results = await Future.wait(futures);
        expect(results, hasLength(3));
        // All should be null due to errors
        expect(results.every((result) => result == null), isTrue);
      });
    });

    group('Configuration', () {
      test('should use correct Whisper model parameter', () async {
        // This test verifies that the service uses the 'tiny' model
        // We can't easily test the actual command without mocking,
        // but we can verify the service doesn't crash with the configuration
        
        const audioPath = '/tmp/test_audio.wav';
        
        try {
          await service.transcribeAudio(audioPath);
          fail('Expected exception for non-existent file');
        } catch (e) {
          // The error should be about the file or Whisper, not about invalid parameters
          expect(e.toString(), isNot(contains('invalid option')));
          expect(e.toString(), isNot(contains('unknown argument')));
        }
      });

      test('should request JSON output format', () async {
        // Similar to above, we verify the service configuration doesn't cause parameter errors
        const audioPath = '/tmp/test_audio.wav';
        
        try {
          await service.transcribeAudio(audioPath);
          fail('Expected exception for non-existent file');
        } catch (e) {
          // The error should not be about output format
          expect(e.toString(), isNot(contains('output_format')));
          expect(e.toString(), isNot(contains('invalid format')));
        }
      });
    });
  });
}
