import 'package:flutter_test/flutter_test.dart';

// Import all test files
import 'services/ai_analysis_service_test.dart' as ai_analysis_tests;
import 'services/ffmpeg_service_test.dart' as ffmpeg_tests;
import 'services/whisper_service_test.dart' as whisper_tests;
import 'controllers/video_clipper_controller_test.dart' as controller_tests;
import 'integration/ai_clipper_integration_test.dart' as integration_tests;

void main() {
  group('AI Clipper Test Suite', () {
    group('Service Tests', () {
      ai_analysis_tests.main();
      ffmpeg_tests.main();
      whisper_tests.main();
    });

    group('Controller Tests', () {
      controller_tests.main();
    });

    group('Integration Tests', () {
      integration_tests.main();
    });
  });
}
