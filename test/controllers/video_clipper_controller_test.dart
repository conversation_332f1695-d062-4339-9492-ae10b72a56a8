import 'package:flutter_test/flutter_test.dart';
import 'package:aiclipper/controllers/video_clipper_controller.dart';
import 'dart:io';

void main() {
  group('VideoClipperController', () {
    late VideoClipperController controller;

    setUpAll(() {
      TestWidgetsFlutterBinding.ensureInitialized();
    });

    setUp(() {
      controller = VideoClipperController();
    });

    group('processVideo', () {
      test('should throw exception for non-existent video file', () async {
        const videoPath = '/non/existent/video.mp4';

        expect(
          () => controller.processVideo(videoPath),
          throwsA(
            predicate(
              (e) =>
                  e is Exception &&
                  e.toString().contains('Video file not found'),
            ),
          ),
        );
      });

      test('should handle empty video path', () async {
        expect(() => controller.processVideo(''), throwsA(isA<Exception>()));
      });

      test('should handle invalid video file', () async {
        // Create a temporary text file (not a video)
        final tempFile = File('/tmp/fake_video.mp4');
        await tempFile.writeAsString('This is not a video file');

        try {
          expect(
            () => controller.processVideo(tempFile.path),
            throwsA(
              predicate(
                (e) =>
                    e is Exception &&
                    e.toString().contains('Failed to process video'),
              ),
            ),
          );
        } finally {
          // Clean up
          if (await tempFile.exists()) {
            await tempFile.delete();
          }
        }
      });

      test('should return list of clip suggestions when successful', () async {
        // This test would require a real video file and all dependencies
        // For now, we test the error handling path

        const videoPath = '/tmp/test_video.mp4';

        try {
          final result = await controller.processVideo(videoPath);
          // If we get here, processing was successful
          expect(result, isA<List<Map<String, dynamic>>>());
          expect(result, isNotNull);
        } catch (e) {
          // Expected when video file doesn't exist or dependencies are missing
          expect(e, isA<Exception>());
        }
      });
    });

    group('createClip', () {
      test('should throw exception for invalid time range', () async {
        const videoPath = '/tmp/test_video.mp4';

        // Test negative start time
        expect(
          () => controller.createClip(videoPath, -1.0, 10.0),
          throwsA(
            predicate(
              (e) =>
                  e is Exception && e.toString().contains('Invalid time range'),
            ),
          ),
        );

        // Test end time before start time
        expect(
          () => controller.createClip(videoPath, 10.0, 5.0),
          throwsA(
            predicate(
              (e) =>
                  e is Exception && e.toString().contains('Invalid time range'),
            ),
          ),
        );

        // Test equal start and end times
        expect(
          () => controller.createClip(videoPath, 10.0, 10.0),
          throwsA(
            predicate(
              (e) =>
                  e is Exception && e.toString().contains('Invalid time range'),
            ),
          ),
        );
      });

      test('should throw exception for non-existent video file', () async {
        const videoPath = '/non/existent/video.mp4';

        expect(
          () => controller.createClip(videoPath, 0.0, 10.0),
          throwsA(
            predicate(
              (e) =>
                  e is Exception &&
                  e.toString().contains('Video file not found'),
            ),
          ),
        );
      });

      test('should generate output path when not provided', () async {
        const videoPath = '/tmp/test_video.mp4';

        try {
          await controller.createClip(videoPath, 0.0, 10.0);
          fail('Expected exception for non-existent file');
        } catch (e) {
          // Should fail due to missing file, not due to output path issues
          expect(e.toString(), contains('Video file not found'));
        }
      });

      test('should use provided output path', () async {
        const videoPath = '/tmp/test_video.mp4';
        const outputPath = '/tmp/custom_output.mp4';

        try {
          await controller.createClip(
            videoPath,
            0.0,
            10.0,
            outputPath: outputPath,
          );
          fail('Expected exception for non-existent file');
        } catch (e) {
          // Should fail due to missing file, not due to output path issues
          expect(e.toString(), contains('Video file not found'));
        }
      });

      test('should handle special characters in paths', () async {
        const videoPath = '/tmp/test video with spaces & symbols!.mp4';
        const outputPath = '/tmp/output with spaces.mp4';

        expect(
          () => controller.createClip(
            videoPath,
            0.0,
            10.0,
            outputPath: outputPath,
          ),
          throwsA(isA<Exception>()),
        );
      });

      test('should validate time parameters', () async {
        const videoPath = '/tmp/test_video.mp4';

        // Test very small duration
        expect(
          () => controller.createClip(videoPath, 0.0, 0.1),
          throwsA(isA<Exception>()),
        );

        // Test very large times
        expect(
          () => controller.createClip(videoPath, 0.0, 999999.0),
          throwsA(isA<Exception>()),
        );

        // Test fractional seconds (should be valid)
        expect(
          () => controller.createClip(videoPath, 1.5, 3.7),
          throwsA(isA<Exception>()), // Will fail due to missing file
        );
      });
    });

    group('getVideoInfo', () {
      test('should return null for non-existent video file', () async {
        const videoPath = '/non/existent/video.mp4';

        final result = await controller.getVideoInfo(videoPath);
        expect(result, isNull);
      });

      test('should return null for invalid video file', () async {
        // Create a temporary text file (not a video)
        final tempFile = File('/tmp/fake_video.mp4');
        await tempFile.writeAsString('This is not a video file');

        try {
          final result = await controller.getVideoInfo(tempFile.path);
          expect(result, isNull);
        } finally {
          // Clean up
          if (await tempFile.exists()) {
            await tempFile.delete();
          }
        }
      });

      test('should handle empty path', () async {
        final result = await controller.getVideoInfo('');
        expect(result, isNull);
      });
    });

    group('validateDependencies', () {
      test('should check for Whisper availability', () async {
        final result = await controller.validateDependencies();

        // Result depends on whether Whisper is actually installed
        expect(result, isA<bool>());

        // If Whisper is not installed, should return false
        // If Whisper is installed, should return true
        // We can't predict which, so we just verify it returns a boolean
      });

      test('should handle missing dependencies gracefully', () async {
        // This test verifies that the method doesn't throw exceptions
        expect(() => controller.validateDependencies(), returnsNormally);
      });
    });

    group('Integration scenarios', () {
      test(
        'should handle complete workflow with missing dependencies',
        () async {
          const videoPath = '/tmp/test_video.mp4';

          // Test dependency validation
          final depsValid = await controller.validateDependencies();
          expect(depsValid, isA<bool>());

          // Test video info retrieval
          final videoInfo = await controller.getVideoInfo(videoPath);
          expect(videoInfo, isNull); // Expected for non-existent file

          // Test video processing
          try {
            await controller.processVideo(videoPath);
            fail('Expected exception for non-existent file');
          } catch (e) {
            expect(e, isA<Exception>());
          }

          // Test clip creation
          try {
            await controller.createClip(videoPath, 0.0, 10.0);
            fail('Expected exception for non-existent file');
          } catch (e) {
            expect(e, isA<Exception>());
          }
        },
      );

      test('should handle concurrent operations', () async {
        const videoPath = '/tmp/test_video.mp4';

        // Test multiple concurrent operations
        final futures = [
          controller.getVideoInfo(videoPath),
          controller.validateDependencies(),
          controller.getVideoInfo(videoPath),
        ];

        final results = await Future.wait(futures);
        expect(results, hasLength(3));
        expect(results[0], isNull); // Video info for non-existent file
        expect(results[1], isA<bool>()); // Dependency validation
        expect(results[2], isNull); // Video info for non-existent file
      });

      test('should handle error propagation correctly', () async {
        const videoPath = '/tmp/test_video.mp4';

        // All operations should fail gracefully for non-existent files
        expect(
          () => controller.processVideo(videoPath),
          throwsA(isA<Exception>()),
        );
        expect(
          () => controller.createClip(videoPath, 0.0, 10.0),
          throwsA(isA<Exception>()),
        );

        // These should return null/false instead of throwing
        final videoInfo = await controller.getVideoInfo(videoPath);
        expect(videoInfo, isNull);

        final depsValid = await controller.validateDependencies();
        expect(depsValid, isA<bool>());
      });
    });
  });
}
