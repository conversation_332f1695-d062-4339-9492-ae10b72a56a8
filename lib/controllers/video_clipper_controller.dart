import 'dart:io';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import '../services/ffmpeg_service.dart';
import '../services/whisper_service.dart';
import '../services/ai_analysis_service.dart';

class VideoClipperController {
  final FFmpegService _ffmpegService = FFmpegService();
  final WhisperService _whisperService = WhisperService();
  final AIAnalysisService _aiAnalysisService = AIAnalysisService();

  /// Process a video and return AI-generated clip suggestions
  Future<List<Map<String, dynamic>>> processVideo(String videoPath) async {
    try {
      // 1. Validate video file exists
      final videoFile = File(videoPath);
      if (!await videoFile.exists()) {
        throw Exception('Video file not found: $videoPath');
      }

      // 2. Get video information
      final videoInfo = await _ffmpegService.getVideoInfo(videoPath);
      if (videoInfo == null) {
        throw Exception('Could not get video information');
      }

      final videoDuration = videoInfo['duration'] as double;

      // 3. Extract audio to temporary file
      final tempDir = await getTemporaryDirectory();
      final audioPath = path.join(tempDir.path, 'extracted_audio.wav');

      final audioExtracted = await _ffmpegService.extractAudio(
        videoPath,
        audioPath,
      );
      if (!audioExtracted) {
        throw Exception('Failed to extract audio from video');
      }

      // 4. Transcribe audio with Whisper
      final transcript = await _whisperService.transcribeAudio(audioPath);

      // 5. Analyze transcript for interesting segments
      final clipSuggestions = await _aiAnalysisService.analyzeTranscript(
        transcript,
        videoDuration,
      );

      // 6. Clean up temporary audio file
      try {
        await File(audioPath).delete();
      } catch (e) {
        // Ignore cleanup errors
      }

      // 7. Convert suggestions to JSON format
      return clipSuggestions.map((suggestion) => suggestion.toJson()).toList();
    } catch (e) {
      throw Exception('Failed to process video: $e');
    }
  }

  /// Create a video clip from the specified time range
  Future<String> createClip(
    String videoPath,
    double start,
    double end, {
    String? outputPath,
  }) async {
    try {
      // Validate input
      if (start < 0 || end <= start) {
        throw Exception('Invalid time range: start=$start, end=$end');
      }

      final videoFile = File(videoPath);
      if (!await videoFile.exists()) {
        throw Exception('Video file not found: $videoPath');
      }

      // Generate output path if not provided
      String finalOutputPath;
      if (outputPath != null) {
        finalOutputPath = outputPath;
      } else {
        final videoDir = path.dirname(videoPath);
        final videoName = path.basenameWithoutExtension(videoPath);
        final videoExt = path.extension(videoPath);
        final timestamp = DateTime.now().millisecondsSinceEpoch;
        finalOutputPath = path.join(
          videoDir,
          '${videoName}_clip_${timestamp}${videoExt}',
        );
      }

      // Create the clip
      final success = await _ffmpegService.clipVideo(
        videoPath,
        finalOutputPath,
        start,
        end,
      );

      if (!success) {
        throw Exception('Failed to create video clip');
      }

      // Verify the output file was created
      final outputFile = File(finalOutputPath);
      if (!await outputFile.exists()) {
        throw Exception('Output file was not created: $finalOutputPath');
      }

      return finalOutputPath;
    } catch (e) {
      throw Exception('Failed to create clip: $e');
    }
  }

  /// Get video information including duration
  Future<Map<String, dynamic>?> getVideoInfo(String videoPath) async {
    return await _ffmpegService.getVideoInfo(videoPath);
  }

  /// Validate that all required dependencies are available
  Future<bool> validateDependencies() async {
    try {
      // Check if Whisper is available
      final whisperResult = await Process.run('whisper', ['--help']);
      if (whisperResult.exitCode != 0) {
        return false;
      }

      // FFmpeg availability is checked through the ffmpeg_kit_flutter package
      // which should handle this internally

      return true;
    } catch (e) {
      return false;
    }
  }
}
