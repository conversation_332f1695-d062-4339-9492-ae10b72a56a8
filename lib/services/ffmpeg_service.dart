import 'package:ffmpeg_kit_flutter_new/ffmpeg_kit.dart';
import 'package:ffmpeg_kit_flutter_new/return_code.dart';

class FFmpegService {
  Future<bool> extractAudio(String videoPath, String outputPath) async {
    final session = await FFmpegKit.execute(
      '-i "$videoPath" -vn -acodec pcm_s16le -ar 16000 -ac 1 "$outputPath"',
    );
    final returnCode = await session.getReturnCode();
    return ReturnCode.isSuccess(returnCode);
  }

  Future<bool> clipVideo(
    String videoPath,
    String outputPath,
    double startTime,
    double endTime,
  ) async {
    try {
      final session = await FFmpegKit.execute(
        '-i "$videoPath" -ss $startTime -to $endTime -c copy "$outputPath"',
      );
      final returnCode = await session.getReturnCode();
      return ReturnCode.isSuccess(returnCode);
    } catch (e) {
      print('Error clipping video: $e');
      return false;
    }
  }

  Future<Map<String, dynamic>?> getVideoInfo(String videoPath) async {
    try {
      final session = await FFmpegKit.execute('-i "$videoPath" -hide_banner');
      final output = await session.getOutput();
      // Parse video information from FFmpeg output
      // This is a simplified version - you might want more detailed parsing
      return {'path': videoPath, 'duration': _extractDuration(output ?? '')};
    } catch (e) {
      print('Error getting video info: $e');
      return null;
    }
  }

  double _extractDuration(String ffmpegOutput) {
    // Simple regex to extract duration from FFmpeg output
    final durationRegex = RegExp(r'Duration: (\d{2}):(\d{2}):(\d{2})\.(\d{2})');
    final match = durationRegex.firstMatch(ffmpegOutput);

    if (match != null) {
      final hours = int.parse(match.group(1)!);
      final minutes = int.parse(match.group(2)!);
      final seconds = int.parse(match.group(3)!);
      final centiseconds = int.parse(match.group(4)!);

      return hours * 3600 + minutes * 60 + seconds + centiseconds / 100;
    }

    return 0.0;
  }
}
