import 'dart:io';
import 'dart:convert';

class WhisperService {
  Future<Map<String, dynamic>> transcribeAudio(String audioPath) async {
    try {
      final result = await Process.run('whisper', [
        audioPath,
        '--model', 'tiny',
        '--output_format', 'json'
      ]);
      
      if (result.exitCode != 0) {
        throw Exception('Whisper transcription failed: ${result.stderr}');
      }
      
      return jsonDecode(result.stdout);
    } catch (e) {
      throw Exception('Failed to transcribe: $e');
    }
  }
}