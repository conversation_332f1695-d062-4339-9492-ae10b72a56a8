import 'dart:math';

class AIAnalysisService {
  /// Analyzes a transcript and identifies interesting segments for clipping
  Future<List<ClipSuggestion>> analyzeTranscript(
    Map<String, dynamic> transcript,
    double videoDuration,
  ) async {
    final suggestions = <ClipSuggestion>[];

    // Extract segments from Whisper transcript
    final segments = transcript['segments'] as List<dynamic>? ?? [];

    if (segments.isEmpty) {
      return suggestions;
    }

    // Analyze each segment for interesting content
    for (int i = 0; i < segments.length; i++) {
      final segment = segments[i] as Map<String, dynamic>;
      final text = segment['text'] as String? ?? '';
      final start = (segment['start'] as num?)?.toDouble() ?? 0.0;
      final end = (segment['end'] as num?)?.toDouble() ?? 0.0;

      // Score this segment based on various criteria
      final score = _scoreSegment(text, segments, i);

      if (score > 0.6) {
        // Threshold for interesting content
        suggestions.add(
          ClipSuggestion(
            startTime: _expandStart(start, segments, i),
            endTime: _expandEnd(end, segments, i, videoDuration),
            confidence: score,
            reason: _getReasonForClip(text),
            text: text,
          ),
        );
      }
    }

    // Merge overlapping suggestions and sort by confidence
    final mergedSuggestions = _mergeOverlappingSuggestions(suggestions);
    mergedSuggestions.sort((a, b) => b.confidence.compareTo(a.confidence));

    // Return top 5 suggestions
    return mergedSuggestions.take(5).toList();
  }

  double _scoreSegment(String text, List<dynamic> segments, int index) {
    double score = 0.0;
    final lowerText = text.toLowerCase();

    // Keywords that indicate interesting content
    final interestingKeywords = [
      'amazing',
      'incredible',
      'wow',
      'unbelievable',
      'fantastic',
      'hilarious',
      'funny',
      'laugh',
      'joke',
      'comedy',
      'important',
      'key',
      'crucial',
      'essential',
      'critical',
      'breakthrough',
      'discovery',
      'reveal',
      'secret',
      'surprise',
      'question',
      'answer',
      'explain',
      'how to',
      'tutorial',
      'tip',
      'trick',
      'hack',
      'advice',
      'recommendation',
    ];

    // Emotional indicators
    final emotionalWords = [
      'excited',
      'thrilled',
      'shocked',
      'surprised',
      'confused',
      'angry',
      'frustrated',
      'happy',
      'sad',
      'worried',
    ];

    // Score based on keywords
    for (final keyword in interestingKeywords) {
      if (lowerText.contains(keyword)) {
        score += 0.25;
      }
    }

    for (final emotion in emotionalWords) {
      if (lowerText.contains(emotion)) {
        score += 0.15;
      }
    }

    // Score based on punctuation (excitement, questions)
    if (text.contains('!')) score += 0.15;
    if (text.contains('?')) score += 0.2;
    if (text.contains('...')) score += 0.1;

    // Score based on length (not too short, not too long)
    final wordCount = text.split(' ').length;
    if (wordCount >= 3 && wordCount <= 50) {
      score += 0.15;
    }

    // Boost score for very interesting combinations
    if (lowerText.contains('incredible') && text.contains('!')) {
      score += 0.2;
    }

    // Score based on context (if previous/next segments are also interesting)
    if (index > 0) {
      final prevText =
          (segments[index - 1] as Map<String, dynamic>)['text'] as String? ??
          '';
      if (_hasInterestingKeywords(prevText.toLowerCase())) {
        score += 0.1;
      }
    }

    if (index < segments.length - 1) {
      final nextText =
          (segments[index + 1] as Map<String, dynamic>)['text'] as String? ??
          '';
      if (_hasInterestingKeywords(nextText.toLowerCase())) {
        score += 0.1;
      }
    }

    return min(score, 1.0); // Cap at 1.0
  }

  bool _hasInterestingKeywords(String text) {
    final keywords = ['amazing', 'incredible', 'wow', 'important', 'funny'];
    return keywords.any((keyword) => text.contains(keyword));
  }

  double _expandStart(double start, List<dynamic> segments, int index) {
    // Expand start time to include a bit of context
    final expansion = 2.0; // 2 seconds before
    return max(0.0, start - expansion);
  }

  double _expandEnd(
    double end,
    List<dynamic> segments,
    int index,
    double videoDuration,
  ) {
    // Expand end time to include a bit of context
    final expansion = 3.0; // 3 seconds after
    return min(videoDuration, end + expansion);
  }

  String _getReasonForClip(String text) {
    final lowerText = text.toLowerCase();

    // Check for educational content first (more specific)
    if (lowerText.contains('how to') ||
        lowerText.contains('tutorial') ||
        lowerText.contains('tip') ||
        lowerText.contains('step by step')) {
      return 'Educational content';
    }
    if (lowerText.contains('funny') ||
        lowerText.contains('hilarious') ||
        lowerText.contains('joke') ||
        lowerText.contains('laugh')) {
      return 'Funny moment';
    }
    if (lowerText.contains('important') ||
        lowerText.contains('key') ||
        lowerText.contains('crucial')) {
      return 'Key information';
    }
    if (lowerText.contains('amazing') ||
        lowerText.contains('incredible') ||
        lowerText.contains('wow')) {
      return 'Exciting moment';
    }
    if (text.contains('?')) {
      return 'Important question';
    }

    return 'Interesting content';
  }

  List<ClipSuggestion> _mergeOverlappingSuggestions(
    List<ClipSuggestion> suggestions,
  ) {
    if (suggestions.length <= 1) return suggestions;

    // Sort by start time
    suggestions.sort((a, b) => a.startTime.compareTo(b.startTime));

    final merged = <ClipSuggestion>[];
    ClipSuggestion current = suggestions.first;

    for (int i = 1; i < suggestions.length; i++) {
      final next = suggestions[i];

      // If clips overlap or are very close (within 5 seconds)
      if (next.startTime <= current.endTime + 5.0) {
        // Merge them
        current = ClipSuggestion(
          startTime: current.startTime,
          endTime: max(current.endTime, next.endTime),
          confidence: max(current.confidence, next.confidence),
          reason: current.confidence >= next.confidence
              ? current.reason
              : next.reason,
          text: '${current.text} ${next.text}',
        );
      } else {
        merged.add(current);
        current = next;
      }
    }

    merged.add(current);
    return merged;
  }
}

class ClipSuggestion {
  final double startTime;
  final double endTime;
  final double confidence;
  final String reason;
  final String text;

  ClipSuggestion({
    required this.startTime,
    required this.endTime,
    required this.confidence,
    required this.reason,
    required this.text,
  });

  double get duration => endTime - startTime;

  Map<String, dynamic> toJson() {
    return {
      'startTime': startTime,
      'endTime': endTime,
      'duration': duration,
      'confidence': confidence,
      'reason': reason,
      'text': text,
    };
  }

  @override
  String toString() {
    return 'ClipSuggestion(${startTime.toStringAsFixed(1)}s-${endTime.toStringAsFixed(1)}s, '
        'confidence: ${confidence.toStringAsFixed(2)}, reason: $reason)';
  }
}
